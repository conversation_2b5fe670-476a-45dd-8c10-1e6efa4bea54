Could not resolve all artifacts for configuration ':composeApp:jsCompileClasspath'.
> Could not resolve androidx.lifecycle:lifecycle-viewmodel:2.8.7.
  Required by:
      project ':composeApp'
   > No matching variant of androidx.lifecycle:lifecycle-viewmodel:2.8.7 was found. The consumer was configured to find a library for use during 'kotlin-api', preferably optimized for non-jvm, as well as attribute 'org.jetbrains.kotlin.js.compiler' with value 'ir', attribute 'org.jetbrains.kotlin.klib.packaging' with value 'non-packed', attribute 'org.jetbrains.kotlin.platform.type' with value 'js' but:
       - Variant 'androidxSourcesElements':
           - Incompatible because this component declares documentation for use during 'androidx-multiplatform-docs' and the consumer needed a library for use during 'kotlin-api'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
               - Doesn't say anything about org.jetbrains.kotlin.platform.type (required 'js')
       - Variant 'desktopApiElements-published' declares a library for use during compile-time:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'jvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'desktopRuntimeElements-published' declares a library for use during runtime:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'jvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'desktopSourcesElements-published' declares a component for use during runtime:
           - Incompatible because this component declares documentation, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'jvm' and the consumer needed a library, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosArm64ApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosArm64MetadataElements-published' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosArm64SourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosSimulatorArm64ApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosSimulatorArm64MetadataElements-published' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosSimulatorArm64SourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosX64ApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosX64MetadataElements-published' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'iosX64SourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'libraryVersionMetadata':
           - Incompatible because this component declares documentation for use during 'library-version-metadata' and the consumer needed a library for use during 'kotlin-api'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
               - Doesn't say anything about org.jetbrains.kotlin.platform.type (required 'js')
       - Variant 'linuxX64ApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'linuxX64SourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'macosArm64ApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'macosArm64MetadataElements-published' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'macosArm64SourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'macosX64ApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'macosX64MetadataElements-published' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'macosX64SourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'metadataApiElements' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'common' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'metadataSourcesElements':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'common' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'releaseApiElements-published' declares a library for use during compile-time:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'androidJvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'releaseRuntimeElements-published' declares a library for use during runtime:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'androidJvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'releaseSourcesElements-published' declares a component for use during runtime:
           - Incompatible because this component declares documentation, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'androidJvm' and the consumer needed a library, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
> Could not resolve androidx.lifecycle:lifecycle-runtime-compose:2.8.7.
  Required by:
      project ':composeApp'
   > No matching variant of androidx.lifecycle:lifecycle-runtime-compose:2.8.7 was found. The consumer was configured to find a library for use during 'kotlin-api', preferably optimized for non-jvm, as well as attribute 'org.jetbrains.kotlin.js.compiler' with value 'ir', attribute 'org.jetbrains.kotlin.klib.packaging' with value 'non-packed', attribute 'org.jetbrains.kotlin.platform.type' with value 'js' but:
       - Variant 'androidxSourcesElements':
           - Incompatible because this component declares documentation for use during 'androidx-multiplatform-docs' and the consumer needed a library for use during 'kotlin-api'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
               - Doesn't say anything about org.jetbrains.kotlin.platform.type (required 'js')
       - Variant 'jvmStubsApiElements-published' declares a library for use during compile-time:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'jvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'jvmStubsRuntimeElements-published' declares a library for use during runtime:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'jvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'jvmStubsSourcesElements-published' declares a component for use during runtime:
           - Incompatible because this component declares documentation, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'jvm' and the consumer needed a library, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'libraryVersionMetadata':
           - Incompatible because this component declares documentation for use during 'library-version-metadata' and the consumer needed a library for use during 'kotlin-api'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
               - Doesn't say anything about org.jetbrains.kotlin.platform.type (required 'js')
       - Variant 'linuxx64StubsApiElements-published' declares a library for use during 'kotlin-api':
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'linuxx64StubsSourcesElements-published':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'native' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'metadataApiElements' declares a library:
           - Incompatible because this component declares a component for use during 'kotlin-metadata', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'common' and the consumer needed a component for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'metadataSourcesElements':
           - Incompatible because this component declares documentation for use during 'kotlin-runtime', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'common' and the consumer needed a library for use during 'kotlin-api', as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'releaseApiElements-published' declares a library for use during compile-time:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'androidJvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'releaseRuntimeElements-published' declares a library for use during runtime:
           - Incompatible because this component declares a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'androidJvm' and the consumer needed a component, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')
       - Variant 'releaseSourcesElements-published' declares a component for use during runtime:
           - Incompatible because this component declares documentation, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'androidJvm' and the consumer needed a library, as well as attribute 'org.jetbrains.kotlin.platform.type' with value 'js'
           - Other compatible attributes:
               - Doesn't say anything about its target Java environment (preferred optimized for non-jvm)
               - Doesn't say anything about org.jetbrains.kotlin.js.compiler (required 'ir')
               - Doesn't say anything about org.jetbrains.kotlin.klib.packaging (required 'non-packed')

* Try:
> No matching variant errors are explained in more detail at https://docs.gradle.org/9.0-milestone-1/userguide/variant_model.html#sub:variant-no-match.
> Review the variant matching algorithm at https://docs.gradle.org/9.0-milestone-1/userguide/variant_attributes.html#sec:abm_algorithm.
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.
Deprecated Gradle features were used in this build, making it incompatible with Gradle 10.0.
You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.
For more on this, please refer to https://docs.gradle.org/9.0-milestone-1/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.
BUILD FAILED in 17s
Configuration cache entry stored.

